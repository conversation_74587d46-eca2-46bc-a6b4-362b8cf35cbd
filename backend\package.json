{"name": "backend", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www"}, "dependencies": {"@coze/api": "^1.2.0", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "debug": "~2.6.9", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "~4.16.1", "express-session": "^1.18.1", "http-errors": "~1.6.3", "jade": "~1.11.0", "jsonwebtoken": "^9.0.2", "morgan": "~1.9.1", "multer": "^2.0.1", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "pbkdf2-password": "^1.2.1"}}