require('dotenv').config();

const createError = require('http-errors');
const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const logger = require('morgan');
const cors = require('cors');
const config = require('./config/config');
const mysql = require('mysql2/promise');

// 创建数据库连接池
const pool = mysql.createPool(config.db);

// 测试数据库连接
pool.getConnection()
  .then(connection => {
    console.log('数据库连接成功');
    connection.release();
  })
  .catch(err => {
    console.error('数据库连接失败:', err);
    process.exit(1);
  });

// 导入路由
const indexRouter = require('./routes/index');
const usersRouter = require('./routes/users');
const postsRouter = require('./routes/posts');
const commentsRouter = require('./routes/comments');
const likesRouter = require('./routes/likes');
const followsRouter = require('./routes/follows');
const notificationsRouter = require('./routes/notifications');
const aiDialogRouter = require('./routes/ai-dialog');
const uploadRouter = require('./routes/upload');

const app = express();

// 启用 CORS
app.use(cors(config.cors));

// 配置日志
app.use(logger('dev', {
  stream: {
    write: (message) => {
      console.log(message);
    }
  }
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));

// 将数据库连接池添加到请求对象中
app.use((req, res, next) => {
  req.db = pool;
  next();
});

// 注册路由
app.use('/', indexRouter);
app.use('/api/user', usersRouter);
app.use('/api/posts', postsRouter);
app.use('/api/comments', commentsRouter);
app.use('/api/likes', likesRouter);
app.use('/api/follows', followsRouter);
app.use('/api/notifications', notificationsRouter);
app.use('/api/dialog', aiDialogRouter);
app.use('/api/upload', uploadRouter);

// 处理 404 错误
app.use((req, res, next) => {
  res.status(404).json({
    code: 404,
    message: '接口未找到'
  });
});

// 通用错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误：', err);
  res.status(err.status || 500).json({
    code: err.status || 500,
    message: err.message || '服务器内部错误'
  });
});

// 设置端口
const port = config.server.port;
app.set('port', port);

// 导出 app 之前添加启动日志
if (require.main === module) {
  app.listen(port, () => {
    console.log(`服务器启动成功，监听端口 ${port}`);
    console.log(`环境: ${config.server.env}`);
    console.log(`CORS origin: ${config.cors.origin}`);
  });
}

module.exports = app;
