/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppNavbar: typeof import('./src/components/AppNavbar.vue')['default']
    ContextMenu: typeof import('./src/components/common/ContextMenu.vue')['default']
    FeatureSection: typeof import('./src/components/FeatureSection.vue')['default']
    HeroSection: typeof import('./src/components/HeroSection.vue')['default']
    NotificationMenu: typeof import('./src/components/NotificationMenu.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
