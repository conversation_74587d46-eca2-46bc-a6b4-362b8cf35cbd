<template>
  <div class="feature-wrapper d-flex justify-center">
    <v-container class="feature-section" style="width: 85%">
      <h2 class="text-h4 text-center mb-12 font-weight-bold text-grey-darken-3">
        在这里，你可以
      </h2>

      <v-row>
        <!-- 特性卡片1：发布与分享 -->
        <v-col cols="12" md="4">
          <v-card class="feature-card pa-6" elevation="0">
            <div class="feature-icon mb-4 purple-bg">
              <v-icon color="white">mdi-comment-text-outline</v-icon>
            </div>
            <h3 class="text-h5 mb-3 font-weight-medium">发布与交流</h3>
            <p class="text-body-2 text-grey-darken-1">
              在社区中发布帖子，分享你对MBTI的理解，或提出问题，向其他用户寻求建议或解答。
            </p>
          </v-card>
        </v-col>

        <!-- 特性卡片2：找到志同道合的人 -->
        <v-col cols="12" md="4">
          <v-card class="feature-card pa-6" elevation="0">
            <div class="feature-icon mb-4 blue-bg">
              <v-icon color="white">mdi-account-group-outline</v-icon>
            </div>
            <h3 class="text-h5 mb-3 font-weight-medium">匹配与连接</h3>
            <p class="text-body-2 text-grey-darken-1">
              基于MBTI类型匹配，找到与你相似或互补的人，建立有意义的连接，分享经验和见解。
            </p>
          </v-card>
        </v-col>

        <!-- 特性卡片3：AI性格模拟对话 -->
        <v-col cols="12" md="4">
          <v-card class="feature-card pa-6" elevation="0">
            <div class="feature-icon mb-4 green-bg">
              <v-icon color="white">mdi-robot-outline</v-icon>
            </div>
            <h3 class="text-h5 mb-3 font-weight-medium">AI性格模拟对话</h3>
            <p class="text-body-2 text-grey-darken-1">
              体验与AI的MBTI模拟对话，了解不同性格类型如何看待同一问题，从中获得有趣的视角和启发。
            </p>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
// 无需额外的脚本逻辑
</script>

<style scoped>
.feature-wrapper {
  width: 100%;
  padding-top: 60px;
  padding-bottom: 3rem;
}

.feature-section {
  border-radius: 24px;
}

.feature-card {
  height: 100%;
  border-radius: 16px;
  background-color: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.purple-bg {
  background: linear-gradient(135deg, #d8b5ff, #b57edc);
}

.blue-bg {
  background: linear-gradient(135deg, #90caf9, #5c9ce6);
}

.green-bg {
  background: linear-gradient(135deg, #a5d6a7, #66bb6a);
}

@media (max-width: 960px) {
  .feature-section {
    width: 95% !important;
  }
}

@media (max-width: 600px) {
  .feature-section {
    width: 100% !important;
    border-radius: 0;
  }
}
</style>
