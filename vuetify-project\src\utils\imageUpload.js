import http from '@/utils/http'

/**
 * 处理内容中的base64图片，上传到服务器并替换为URL
 * @param {string} content - 包含base64图片的HTML内容
 * @returns {Promise<string>} - 处理后的内容
 */
export async function processImagesInContent(content) {
  // 匹配所有base64图片的正则表达式
  const base64ImageRegex = /<img[^>]+src="(data:image\/[^;]+;base64,[^"]+)"[^>]*>/g
  
  let processedContent = content
  const matches = [...content.matchAll(base64ImageRegex)]
  
  if (matches.length === 0) {
    return content // 没有base64图片，直接返回
  }
  
  console.log(`发现 ${matches.length} 张base64图片，开始上传...`)
  
  // 并行上传所有图片
  const uploadPromises = matches.map(async (match, index) => {
    const fullImgTag = match[0]
    const base64Data = match[1]
    
    try {
      console.log(`上传第 ${index + 1} 张图片...`)
      
      // 调用上传API
      const response = await http.post('/upload/base64', {
        imageData: base64Data,
        filename: `editor-image-${Date.now()}-${index}`
      })
      
      if (response.data && response.data.url) {
        const imageUrl = response.data.url
        console.log(`第 ${index + 1} 张图片上传成功: ${imageUrl}`)
        
        // 替换base64为URL
        const newImgTag = fullImgTag.replace(base64Data, imageUrl)
        return { original: fullImgTag, replacement: newImgTag }
      } else {
        throw new Error('上传响应格式错误')
      }
    } catch (error) {
      console.error(`第 ${index + 1} 张图片上传失败:`, error)
      throw new Error(`图片上传失败: ${error.message}`)
    }
  })
  
  try {
    // 等待所有图片上传完成
    const results = await Promise.all(uploadPromises)
    
    // 替换所有base64图片为URL
    results.forEach(({ original, replacement }) => {
      processedContent = processedContent.replace(original, replacement)
    })
    
    console.log('所有图片上传完成')
    return processedContent
    
  } catch (error) {
    console.error('图片上传过程中出现错误:', error)
    throw error
  }
}

/**
 * 上传单个base64图片
 * @param {string} base64Data - base64图片数据
 * @param {string} filename - 文件名（可选）
 * @returns {Promise<string>} - 图片URL
 */
export async function uploadBase64Image(base64Data, filename) {
  try {
    const response = await http.post('/upload/base64', {
      imageData: base64Data,
      filename: filename || `image-${Date.now()}`
    })
    
    if (response.data && response.data.url) {
      return response.data.url
    } else {
      throw new Error('上传响应格式错误')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    throw new Error(`图片上传失败: ${error.message}`)
  }
}
