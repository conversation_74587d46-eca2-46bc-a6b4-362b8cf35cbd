const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();
const auth = require('../middleware/auth');
const { success, error } = require('../utils/response');
const { ErrorType } = require('../utils/errorTypes');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../public/images');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 设置存储路径和文件名
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const ext = path.extname(file.originalname);
        const filename = Date.now() + '-' + Math.round(Math.random() * 1E9) + ext;
        cb(null, filename);
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 只允许图片文件
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传图片文件'), false);
    }
};

const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024 // 限制文件大小为 5MB
    }
});

// 上传图片文件接口
router.post('/', auth, upload.single('image'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json(error(ErrorType.PARAM_ERROR.code, '未上传图片'));
        }
        // 构造图片URL
        const imageUrl = `/images/${req.file.filename}`;
        res.json(success({
            url: imageUrl,
            filename: req.file.filename,
            originalName: req.file.originalname,
            size: req.file.size
        }, '上传成功'));
    } catch (err) {
        console.error('图片上传失败：', err);
        res.status(500).json(error(ErrorType.SERVER_ERROR.code, '图片上传失败'));
    }
});

// 处理base64图片上传
router.post('/base64', auth, async (req, res) => {
    try {
        const { imageData, filename } = req.body;

        if (!imageData) {
            return res.status(400).json(error(ErrorType.PARAM_ERROR.code, '请提供图片数据'));
        }

        // 解析base64数据
        const matches = imageData.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);
        if (!matches) {
            return res.status(400).json(error(ErrorType.PARAM_ERROR.code, '无效的图片格式'));
        }

        const imageType = matches[1];
        const base64Data = matches[2];

        // 生成文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const fileName = filename || `image-${uniqueSuffix}.${imageType}`;
        const filePath = path.join(uploadDir, fileName);

        // 保存文件
        fs.writeFileSync(filePath, base64Data, 'base64');

        // 返回图片访问URL
        const imageUrl = `/images/${fileName}`;

        res.json(success({
            url: imageUrl,
            filename: fileName,
            size: fs.statSync(filePath).size
        }, '图片上传成功'));
    } catch (err) {
        console.error('Base64图片上传失败：', err);
        res.status(500).json(error(ErrorType.SERVER_ERROR.code, '图片上传失败'));
    }
});

module.exports = router;