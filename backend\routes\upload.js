const express = require('express');
const multer = require('multer');
const path = require('path');
const router = express.Router();

// 设置存储路径和文件名
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../public/images'));
    },
    filename: function (req, file, cb) {
        const ext = path.extname(file.originalname);
        const filename = Date.now() + '-' + Math.round(Math.random() * 1E9) + ext;
        cb(null, filename);
    }
});

const upload = multer({ storage });

// 上传图片接口
router.post('/', upload.single('image'), (req, res) => {
    if (!req.file) {
        return res.status(400).json({ code: 400, message: '未上传图片' });
    }
    // 构造图片URL
    const imageUrl = `/images/${req.file.filename}`;
    res.json({ code: 200, message: '上传成功', data: { url: imageUrl } });
});

module.exports = router; 