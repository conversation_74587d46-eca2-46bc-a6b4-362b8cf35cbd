<template>
  <v-app-bar
    app
    elevation="0"
    :color="theme.global.current.value.dark ? 'background' : 'white'"
    height="48"
  >
    <v-container class="d-flex align-center px-6" fluid>
      <!-- Logo -->
      <router-link to="/" class="text-decoration-none">
        <span class="text-h6 font-weight-medium text-primary">MBTI-LINK</span>
      </router-link>

      <!-- 导航链接 -->
      <v-spacer></v-spacer>
      <div class="d-flex align-center">
        <v-btn
          v-for="item in navItems"
          :key="item.key"
          :to="item.link"
          variant="text"
          :class="{ 'active-link': activeLink === item.key }"
          @click="setActiveLink(item.key)"
          class="text-body-1 font-weight-regular mx-2"
          style="text-transform: none"
        >
          {{ item.name }}
        </v-btn>
      </div>

      <!-- 用户操作区 -->
      <v-spacer></v-spacer>
      <div class="d-flex align-center">
        <!-- 主题切换按钮 -->
        <v-btn
          variant="text"
          icon
          class="mr-4"
          @click="toggleTheme"
          :title="
            theme.global.current.value.dark
              ? '切换到浅色模式'
              : '切换到深色模式'
          "
        >
          <v-icon>
            {{ theme.global.current.value.dark ? "$sun" : "$moon" }}
          </v-icon>
        </v-btn>

        <template v-if="!isLogin">
          <v-btn
            variant="text"
            to="/login-register?tab=login"
            class="text-body-1 font-weight-regular"
            style="text-transform: none"
          >
            登录
          </v-btn>
          <v-btn
            color="primary"
            to="/login-register?tab=register"
            class="text-body-1 font-weight-regular ml-2"
            style="text-transform: none"
          >
            注册
          </v-btn>
        </template>

        <v-menu v-else location="bottom end">
          <template v-slot:activator="{ props }">
            <v-btn variant="text" v-bind="props" class="text-body-1">
              {{ userName }}
              <v-icon end>mdi-chevron-down</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item @click="goToUserProfile">
              <v-list-item-title>个人中心</v-list-item-title>
            </v-list-item>
            <v-list-item @click="logout">
              <v-list-item-title>退出登录</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </v-container>
  </v-app-bar>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { useTheme } from "vuetify";
import { useUserStore } from "@/stores/user";
import { computed } from "vue";

const router = useRouter();
const theme = useTheme();
const activeLink = ref("home");
const userStore = useUserStore();

const navItems = [
  { name: "首页", link: "/", key: "home" },
  { name: "社区", link: "/community", key: "community" },
  { name: "与MBTI模拟对话", link: "/chat", key: "chat" },
  { name: "关于MBTI", link: "/about", key: "about" },
];

const setActiveLink = (key) => {
  activeLink.value = key;
};

// 切换主题
const toggleTheme = () => {
  theme.global.name.value = theme.global.current.value.dark ? "light" : "dark";
  // 保存主题偏好到本地存储
  localStorage.setItem("theme-preference", theme.global.name.value);
};

// 初始化主题
const initTheme = () => {
  const savedTheme = localStorage.getItem("theme-preference");
  if (savedTheme) {
    theme.global.name.value = savedTheme;
  }
};

// 在组件挂载时初始化主题
initTheme();

const isLogin = computed(() => userStore.isLoggedIn);
const userName = computed(
  () => userStore.userInfo?.username || userStore.userInfo?.name || "用户"
);

const goToUserProfile = () => {
  router.push("/profile");
};

const logout = () => {
  userStore.logout();
  router.push("/");
};
</script>

<style scoped>
.active-link {
  color: rgb(var(--v-theme-primary)) !important;
}

.v-btn {
  letter-spacing: 0;
}
</style>
