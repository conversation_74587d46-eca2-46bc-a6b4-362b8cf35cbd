
<template>
  <v-container fluid class="pa-0">
    <!-- 顶部横幅 -->
    <v-sheet color="background" class="py-16 text-center">
      <h1 class="text-h2 font-weight-medium mb-2">探索 MBTI 社区</h1>
      <p
        class="text-subtitle-1 text-medium-emphasis mx-auto"
        style="max-width: 600px"
      >
        欢迎加入MBTI社区，参与对话、分享体验，发现与你相似的个性类型。
      </p>
    </v-sheet>

    <!-- 分割线 -->
    <v-divider></v-divider>

    <!-- 主内容区 -->
    <v-container class="py-8">
      <v-row>
        <!-- 左侧筛选区 -->
        <v-col cols="12" md="3">
          <!-- MBTI类型筛选 -->
          <v-card class="mbti-card" elevation="2" rounded="xl">
            <div class="text-h5 font-weight-medium mb-4">MBTI类型筛选</div>
            <div class="mbti-grid mb-10">
              <template
                v-for="group in [
                  { name: '分析家', types: ['INTJ', 'INTP', 'ENTJ', 'ENTP'] },
                  { name: '外交家', types: ['INFJ', 'INFP', 'ENFJ', 'ENFP'] },
                  { name: '守护者', types: ['ISTJ', 'ISFJ', 'ESTJ', 'ESFJ'] },
                  { name: '探险家', types: ['ISTP', 'ISFP', 'ESTP', 'ESFP'] },
                ]"
                :key="group.name"
              >
                <v-row dense class="mb-3">
                  <v-col
                    v-for="type in group.types"
                    :key="type"
                    cols="6"
                    class="d-flex justify-center"
                  >
                    <v-chip
                      :color="getMbtiTypeColor(type)"
                      size="large"
                      variant="flat"
                      class="mbti-chip"
                      @click="toggleMbtiFilter(type)"
                      style="width: 100px; font-weight: 600"
                      :text-color="
                        selectedMbtiTypes.includes(type)
                          ? 'white'
                          : getMbtiTypeColor(type)
                      "
                      :class="{
                        'mbti-chip-selected': selectedMbtiTypes.includes(type),
                      }"
                    >
                      {{ type }}
                    </v-chip>
                  </v-col>
                </v-row>
              </template>
            </div>
          </v-card>
        </v-col>

        <!-- 中间内容区 -->
        <v-col cols="12" md="9">
          <!-- 搜索和发帖 -->
          <div class="d-flex align-center justify-space-between mb-6">
            <v-text-field
              v-model="searchQuery"
              prepend-inner-icon="mdi-magnify"
              placeholder="搜索帖子..."
              variant="outlined"
              density="comfortable"
              hide-details
              class="search-field"
              style="max-width: 300px"
            ></v-text-field>
            <v-btn
              color="accent"
              prepend-icon="mdi-plus"
              variant="flat"
              size="large"
              @click="router.push('/post/create')"
            >
              发布新帖子
            </v-btn>
          </div>

          <!-- 帖子列表 -->
          <div v-if="posts.length === 0" class="text-center pa-4">暂无帖子</div>
          <v-row v-else>
            <v-col v-for="post in posts" :key="post.id" cols="12" md="6">
              <v-card variant="flat" class="post-card mb-4" rounded="lg">
                <v-card-item>
                  <template v-slot:prepend>
                    <v-avatar color="primary" size="40">
                      <template v-if="post.avatar">
                        <v-img :src="post.avatar" alt="avatar"></v-img>
                      </template>
                      <template v-else>
                        {{ getNameInitial(post.username) }}
                      </template>
                    </v-avatar>
                  </template>
                  <div class="d-flex align-center justify-space-between w-100">
                    <div>
                      <div class="d-flex align-center">
                        <span class="text-subtitle-1 font-weight-medium">{{
                          post.username
                        }}</span>
                        <v-chip
                          :color="getMbtiTypeColor(post.mbti_type)"
                          size="x-small"
                          class="ml-2"
                          variant="flat"
                          text-color="white"
                          v-if="post.mbti_type"
                        >
                          {{ post.mbti_type }}
                        </v-chip>
                      </div>
                      <span class="text-caption text-medium-emphasis">{{
                        post.time
                      }}</span>
                    </div>
                    <ContextMenu
                      :is-owner="post.author_id === userStore.userInfo?.id"
                      @delete="handlePostDelete(post.id)"
                      @report="handleReport('post', post.id)"
                    />
                  </div>
                </v-card-item>

                <v-card-text>
                  <div class="text-h6 mb-2">{{ post.title }}</div>
                  <p class="text-body-1 text-medium-emphasis mb-4">
                    {{ getSafeSummary(post.content, 50) }}
                  </p>
                  <div class="d-flex align-center">
                    <v-chip-group>
                      <v-chip
                        v-for="tag in post.tags"
                        :key="tag"
                        size="small"
                        variant="outlined"
                        class="me-2"
                      >
                        {{ tag }}
                      </v-chip>
                    </v-chip-group>
                  </div>
                </v-card-text>

                <v-card-actions class="px-4 pb-4">
                  <v-btn
                    variant="text"
                    density="comfortable"
                    prepend-icon="mdi-thumb-up-outline"
                    class="me-2"
                    @click="handleLike(post.id)"
                  >
                    {{ post.like_count || 0 }}
                  </v-btn>
                  <v-btn
                    variant="text"
                    density="comfortable"
                    prepend-icon="mdi-comment-outline"
                    class="me-2"
                    @click="handleComment(post.id)"
                  >
                    {{ post.comments_count || 0 }}
                  </v-btn>
                  <v-spacer></v-spacer>
                  <v-btn
                    variant="text"
                    color="primary"
                    density="comfortable"
                    @click="viewPostDetail(post.id)"
                  >
                    阅读更多
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-col>
          </v-row>

          <!-- 分页组件 -->
          <div class="d-flex justify-center mt-6">
            <v-pagination
              v-model="currentPage"
              :length="Math.ceil(totalItems / pageSize)"
              :total-visible="7"
              rounded="lg"
            ></v-pagination>
          </div>
        </v-col>
      </v-row>
    </v-container>

    <!-- 提示信息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      location="top"
      :timeout="3000"
    >
      {{ snackbar.text }}
    </v-snackbar>
  </v-container>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { getSafeSummary } from "@/utils/summary";
import { useUserStore } from "@/stores/user";
import { useLikeStore } from "@/stores/like";
import { usePostStore } from "@/stores/post";
import { storeToRefs } from "pinia";
import { mbtiTypes, getMbtiTypeColor } from "@/utils/mbti";
import { getNameInitial } from "@/utils/user";
import ContextMenu from "@/components/common/ContextMenu.vue";

const router = useRouter();
const userStore = useUserStore();
const likeStore = useLikeStore();
const postStore = usePostStore();

const { getPostLikeStatus } = storeToRefs(likeStore);
const { posts, pagination, loading, error } = storeToRefs(postStore);

const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = computed(() => pagination.value.total);
const searchQuery = ref("");
const selectedMbtiTypes = ref([]);

// 提示信息
const snackbar = reactive({
  show: false,
  text: "",
  color: "success",
});

// 获取帖子列表
async function fetchPosts() {
  try {
    const res = await postStore.getPosts({
      page: currentPage.value,
      pageSize: pageSize.value,
      mbtiTypes: selectedMbtiTypes.value,
      search: searchQuery.value,
    });

    if (!res.success) {
      snackbar.text = res.message || "获取帖子失败";
      snackbar.color = "error";
      snackbar.show = true;
    }

    // 批量请求所有帖子的点赞状态
    await fetchAllPostsLikeStatus();
  } catch (e) {
    snackbar.text = e.message || "获取帖子失败";
    snackbar.color = "error";
    snackbar.show = true;
  }
}

// 切换MBTI筛选
const toggleMbtiFilter = (type) => {
  const index = selectedMbtiTypes.value.indexOf(type);
  if (index === -1) {
    selectedMbtiTypes.value.push(type);
  } else {
    selectedMbtiTypes.value.splice(index, 1);
  }
  console.log("当前选中的MBTI类型：", selectedMbtiTypes.value);
};

// 查看帖子详情
const viewPostDetail = (postId) => {
  router.push(`/post/${postId}`);
};

// 批量请求所有帖子的点赞状态
async function fetchAllPostsLikeStatus() {
  if (!userStore.isLoggedIn || !userStore.userInfo) return;
  const userId = userStore.userInfo.id;
  for (const post of posts.value) {
    await likeStore.fetchPostLikeStatus(post.id, userId);
  }
}

// 点赞按钮点击事件
function handleLike(postId) {
  if (!userStore.isLoggedIn || !userStore.userInfo) {
    router.push("/login-register?tab=login");
    return;
  }
  // 这里调用点赞逻辑
  const status = likeStore.getPostLikeStatus(postId, userStore.userInfo.id);
  if (status?.has_liked) {
    likeStore.unlikePost(postId, userStore.userInfo.id);
  } else {
    likeStore.likePost(postId, userStore.userInfo.id);
  }
}

// 评论按钮点击事件
function handleComment(postId) {
  if (!userStore.isLoggedIn || !userStore.userInfo) {
    router.push("/login-register?tab=login");
    return;
  }
  // 跳转到帖子详情页并聚焦评论区
  router.push(`/post/${postId}`);
}

// 处理帖子删除
const handlePostDelete = async (postId) => {
  try {
    await postStore.deletePost(postId);
    snackbar.text = "帖子已删除";
    snackbar.color = "success";
    snackbar.show = true;
    // 重新获取帖子列表
    fetchPosts();
  } catch (e) {
    snackbar.text = e.message || "删除失败";
    snackbar.color = "error";
    snackbar.show = true;
  }
};

// 处理举报
const handleReport = (type, id) => {
  // TODO: 实现举报功能
  snackbar.text = "举报功能开发中";
  snackbar.color = "info";
  snackbar.show = true;
};

onMounted(fetchPosts);

// 监听页码和搜索
watch([currentPage, searchQuery], () => {
  currentPage.value = 1;
  fetchPosts();
});

// 深度监听 MBTI 筛选
watch(
  () => selectedMbtiTypes.value,
  () => {
    currentPage.value = 1;
    fetchPosts();
  },
  { deep: true }
);
</script>

<style scoped>
.mbti-grid {
  width: 100%;
}
.mbti-chip {
  border-radius: 999px !important;
  font-size: 1.1rem;
  margin-bottom: 4px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}
.mbti-chip:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.post-card {
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  min-height: 250px;
  display: flex;
  flex-direction: column;
}

.post-card .v-card-text {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.post-card .v-card-actions {
  margin-top: auto;
}

.post-card:hover {
  border-color: rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.search-field :deep(.v-field__input) {
  padding-top: 8px;
  padding-bottom: 8px;
}

.v-avatar {
  color: white;
  font-weight: 500;
}

.mbti-card {
  padding: 32px 24px 24px 24px;
  border-radius: 24px;
  box-shadow: 0 4px 24px 0 rgba(60, 60, 60, 0.06);
  margin-right: 32px;
}

.mbti-chip {
  border-radius: 999px !important;
  font-size: 1.1rem;
  margin-bottom: 4px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}
.mbti-chip:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.mbti-chip-selected {
  filter: brightness(0.85);
  /* 选中时更深色 */
}
</style> 
