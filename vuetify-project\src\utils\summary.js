import DOMPurify from 'dompurify'

/**
 * 生成安全的纯文本摘要
 * @param {string} rawHtml - 原始富文本内容
 * @param {number} length - 摘要长度，默认100
 * @returns {string} 摘要
 */
export function getSafeSummary(rawHtml, length = 100) {
    if (!rawHtml) return '';

    // 1. 清除危险标签
    const safeHtml = DOMPurify.sanitize(rawHtml);

    // 2. 检查是否包含图片
    const imageRegex = /<img[^>]*>/gi;
    const imageMatches = safeHtml.match(imageRegex);
    const imageCount = imageMatches ? imageMatches.length : 0;

    // 3. 转为纯文本
    const plainText = safeHtml.replace(/<[^>]+>/g, '').trim();

    // 4. 如果没有文本内容但有图片，返回图片提示
    if (!plainText && imageCount > 0) {
        return imageCount === 1 ? '[图片]' : `[${imageCount}张图片]`;
    }

    // 5. 如果有文本内容，正常处理
    if (plainText) {
        const summary = plainText.length > length ? plainText.slice(0, length) + '...' : plainText;
        // 如果还有图片，在摘要后添加图片提示
        if (imageCount > 0) {
            const imageHint = imageCount === 1 ? ' [图片]' : ` [${imageCount}张图片]`;
            return summary + imageHint;
        }
        return summary;
    }

    // 6. 既没有文本也没有图片
    return '';
}