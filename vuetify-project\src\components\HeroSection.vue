<template>
  <div class="hero-wrapper d-flex justify-center">
    <v-container class="hero-section pa-0" style="width: 85%">
      <v-row no-gutters>
        <v-col
          cols="12"
          md="6"
          class="hero-content d-flex flex-column justify-center pa-8"
        >
          <h1 class="text-h3 font-weight-bold mb-4 primary--text">
            探索你的MBTI性格类型
          </h1>
          <p class="text-h6 mb-6 font-weight-bold text-grey-darken-2">
            加入MBTI-LINK，与志同道合的人交流
          </p>
          <p class="text-body-1 mb-8">
            MBTI-LINK是一个基于MBTI性格类型的社交平台，帮助你了解自己的性格特点，找到与你相似或互补的人，分享经验和见解，共同成长。
          </p>
          <div class="d-flex">
            <v-btn
              v-if="!userStore.isLoggedIn"
              color="primary"
              size="large"
              class="rounded-pill mr-4"
              elevation="2"
              :to="{ path: '/login-register', query: { tab: 'login' } }"
            >
              立即加入
            </v-btn>
            <v-btn
              variant="outlined"
              color="primary"
              size="large"
              class="rounded-pill"
              :to="'/community'"
            >
              了解更多
            </v-btn>
          </div>
        </v-col>
        <v-col
          cols="12"
          md="6"
          class="hero-image-container d-flex align-center justify-center"
        >
          <v-img
            src="@/assets/image.png"
            max-width="100%"
            height="auto"
            cover
            class="hero-image"
          />
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { useUserStore } from "@/stores/user";
import { useRouter } from "vue-router";

const userStore = useUserStore();
const router = useRouter();
</script>

<style scoped>
.hero-wrapper {
  width: 100%;
  padding-top: 3rem;
}

.hero-section {
  position: relative;
  overflow: hidden;
  border-radius: 24px;
}

.hero-content {
  z-index: 2;
}

.hero-content h1 {
  background: linear-gradient(90deg, #d8b5ff, #90caf9);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.hero-image-container {
  position: relative;
  overflow: hidden;
  padding: 1.5rem;
}

.hero-image {
  border-radius: 20px;
  transition: transform 0.3s ease;
}

.hero-image:hover {
  transform: scale(1.02);
}

@media (max-width: 960px) {
  .hero-section {
    min-height: auto;
    width: 95% !important;
    padding: 2rem 0;
  }

  .hero-content {
    padding: 2rem 1rem;
    text-align: center;
  }

  .hero-content .d-flex {
    justify-content: center;
  }
}

@media (max-width: 600px) {
  .hero-section {
    width: 100% !important;
    border-radius: 0;
  }
}
</style>
