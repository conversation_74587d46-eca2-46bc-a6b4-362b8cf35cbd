// MBTI类型数据
export const mbtiTypes = [
    // 紫人组(NT) - 理性主义
    { name: "INTJ", color: "#E1BEE7" }, // 浅紫色
    { name: "INTP", color: "#E1BEE7" },
    { name: "ENTJ", color: "#E1BEE7" },
    { name: "ENTP", color: "#E1BEE7" },

    // 绿人组(NF) - 理想主义
    { name: "INFJ", color: "#C8E6C9" }, // 浅绿色
    { name: "INFP", color: "#C8E6C9" },
    { name: "ENFJ", color: "#C8E6C9" },
    { name: "ENFP", color: "#C8E6C9" },

    // 蓝人组(SJ) - 传统主义
    { name: "ISTJ", color: "#BBDEFB" }, // 浅蓝色
    { name: "ISFJ", color: "#BBDEFB" },
    { name: "ESTJ", color: "#BBDEFB" },
    { name: "ESFJ", color: "#BBDEFB" },

    // 黄人组(SP) - 经验主义
    { name: "ISTP", color: "#fce38a" }, // 浅黄色
    { name: "ISFP", color: "#fce38a" },
    { name: "ESTP", color: "#fce38a" },
    { name: "ESFP", color: "#fce38a" },
];

/**
 * 获取MBTI类型的颜色
 * @param {string} type - MBTI类型代码
 * @returns {string} 颜色代码
 */
export function getMbtiTypeColor(type) {
    const mbtiType = mbtiTypes.find((t) => t.name === type);
    return mbtiType?.color || "#E0E0E0"; // 如果找不到类型，返回浅灰色
} 