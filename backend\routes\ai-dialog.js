const express = require('express');
const router = express.Router();
const { CozeAPI, COZE_CN_BASE_URL, ChatEventType } = require('@coze/api');
const aiConfig = require('../config/ai-config');
const pool = require('../setup_db');
const auth = require('../middleware/auth');

// 创建Coze客户端实例
const cozeClient = new CozeAPI({
    token: aiConfig.coze.token,
    baseURL: COZE_CN_BASE_URL,
});

// 获取用户的对话历史
router.get('/history', auth, async (req, res) => {
    try {
        const userId = req.user.id;
        const [conversations] = await pool.query(
            `SELECT c.* 
             FROM conversations c 
             WHERE c.user_id = ? 
             ORDER BY c.started_at DESC`,
            [userId]
        );

        // 获取每个对话的最后一条消息
        for (let conv of conversations) {
            const [lastMessage] = await pool.query(
                `SELECT content FROM messages 
                 WHERE conversation_id = ? 
                 ORDER BY timestamp DESC LIMIT 1`,
                [conv.id]
            );
            conv.lastMessage = lastMessage[0]?.content || '';
        }

        res.json({
            code: 200,
            data: conversations
        });
    } catch (error) {
        console.error('获取对话历史失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取对话历史失败'
        });
    }
});

// 获取单个对话的消息列表
router.get('/conversation/:id', auth, async (req, res) => {
    try {
        const conversationId = req.params.id;
        const userId = req.user.id;

        // 验证对话所属权
        const [conversation] = await pool.query(
            'SELECT * FROM conversations WHERE id = ? AND user_id = ?',
            [conversationId, userId]
        );

        if (!conversation[0]) {
            return res.status(403).json({
                code: 403,
                message: '无权访问该对话'
            });
        }

        // 获取消息列表
        const [messages] = await pool.query(
            'SELECT * FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC',
            [conversationId]
        );

        res.json({
            code: 200,
            data: messages
        });
    } catch (error) {
        console.error('获取对话消息失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取对话消息失败'
        });
    }
});

// AI对话接口
router.post('/chat', auth, async (req, res) => {
    const { message } = req.body;
    const userId = req.user.id;
    console.log(`开始处理AI对话请求 - 用户ID: ${userId}`);

    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();

        let conversationId = req.body.conversation_id;

        // 如果没有会话ID，创建新会话
        if (!conversationId) {
            const summary = message ? message.slice(0, 30) : null;
            const [result] = await connection.query(
                `INSERT INTO conversations (user_id, title, summary, started_at)
                 VALUES (?, ?, ?, NOW())`,
                [userId, `与AI的对话`, summary]
            );
            conversationId = result.insertId;
            console.log(`创建新对话 - ID: ${conversationId}`);
        }

        // 保存用户消息
        await connection.query(
            `INSERT INTO messages (conversation_id, content, sender, timestamp)
             VALUES (?, ?, 'user', NOW())`,
            [conversationId, message]
        );

        // 查询历史消息，拼接 additional_messages
        const [history] = await connection.query(
            'SELECT sender, content FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC',
            [conversationId]
        );
        const additional_messages = history.map(msg => ({
            role: msg.sender === 'user' ? 'user' : 'assistant',
            content: msg.content
        }));

        // 调用AI接口
        console.log('【调试】AI请求参数：', {
            bot_id: aiConfig.coze.bot_id,
            user_id: userId.toString(),
            additional_messages
        });
        const stream = await cozeClient.chat.stream({
            bot_id: aiConfig.coze.bot_id,
            user_id: userId.toString(),
            additional_messages
        });

        // 设置响应头，支持流式传输
        res.setHeader('Content-Type', 'text/plain; charset=utf-8');
        res.setHeader('Transfer-Encoding', 'chunked');

        let fullResponse = '';
        console.log('【调试】开始流式接收AI回复...');
        for await (const part of stream) {
            if (part.event === ChatEventType.CONVERSATION_MESSAGE_DELTA) {
                const content = part.data.content;
                fullResponse += content;
                res.write(content);
            }
        }
        console.log('【调试】AI回复内容：', fullResponse);

        // 保存AI回复
        await connection.query(
            `INSERT INTO messages (conversation_id, content, sender, timestamp)
             VALUES (?, ?, 'ai', NOW())`,
            [conversationId, fullResponse]
        );

        await connection.commit();
        console.log(`AI对话完成 - 对话ID: ${conversationId}`);
        res.end();

    } catch (error) {
        if (connection) {
            await connection.rollback();
        }
        console.error("AI对话出错:", error);
        res.status(500).json({
            code: 500,
            message: 'AI服务出错',
            error: error.message
        });
    } finally {
        if (connection) {
            connection.release();
        }
    }
});

// 清空当前用户所有会话和消息
router.post('/clear', auth, async (req, res) => {
    const userId = req.user.id;
    let connection;
    try {
        connection = await pool.getConnection();
        await connection.beginTransaction();
        // 查找该用户所有会话id
        const [convs] = await connection.query('SELECT id FROM conversations WHERE user_id = ?', [userId]);
        const ids = convs.map(c => c.id);
        if (ids.length > 0) {
            // 删除所有相关消息
            await connection.query('DELETE FROM messages WHERE conversation_id IN (?)', [ids]);
            // 删除所有会话
            await connection.query('DELETE FROM conversations WHERE id IN (?)', [ids]);
        }
        await connection.commit();
        res.json({ code: 200, message: '历史记录已清空' });
    } catch (error) {
        if (connection) await connection.rollback();
        console.error('清空历史失败:', error);
        res.status(500).json({ code: 500, message: '清空历史失败' });
    } finally {
        if (connection) connection.release();
    }
});

module.exports = router; 