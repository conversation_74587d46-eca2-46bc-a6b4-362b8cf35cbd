<template>
  <v-container
    class="py-10 d-flex justify-center align-center"
    style="min-height: 80vh; background: #faf9f6"
  >
    <v-card
      elevation="2"
      class="pa-8"
      style="width: 100%; max-width: 540px; border-radius: 18px"
    >
      <div class="d-flex align-center mb-6">
        <v-icon color="primary" size="32" class="mr-2">mdi-pencil</v-icon>
        <span class="text-h5 font-weight-bold">发布新帖子</span>
      </div>
      <v-form ref="postForm" v-model="formValid">
        <v-text-field
          v-model="post.title"
          label="标题"
          :rules="[(v) => !!v || '请输入标题']"
          variant="outlined"
          class="mb-4"
          density="comfortable"
        ></v-text-field>

        <QuillEditor
          v-model:content="post.content"
          contentType="html"
          theme="snow"
          class="mb-4 quill-editor-style"
          style="min-height: 200px; background: #fff; border-radius: 8px"
          :toolbar="toolbarOptions"
          @image-added="handleImageAdded"
        />

        <v-btn
          color="primary"
          :loading="posting"
          :disabled="!formValid"
          @click="submitPost"
          block
          size="large"
          class="mt-2"
          style="letter-spacing: 2px"
        >
          发布
        </v-btn>
      </v-form>
      <v-snackbar
        v-model="snackbar.show"
        :color="snackbar.color"
        location="top"
        :timeout="3000"
      >
        {{ snackbar.text }}
      </v-snackbar>
    </v-card>
  </v-container>
</template>

<script setup>
import { ref, reactive } from "vue";
import { QuillEditor } from "@vueup/vue-quill";
import "@vueup/vue-quill/dist/vue-quill.snow.css";
import http from "@/plugins/axios";
import { useRouter } from "vue-router";

const router = useRouter();
const postForm = ref(null);
const formValid = ref(false);
const posting = ref(false);

const post = reactive({
  title: "",
  content: "",
});

const snackbar = reactive({
  show: false,
  text: "",
  color: "success",
});

const toolbarOptions = [
  [{ header: [1, 2, false] }],
  ["bold", "italic", "underline", "strike"],
  ["blockquote", "code-block"],
  [{ list: "ordered" }, { list: "bullet" }],
  ["link", "image"],
  ["clean"],
];

const submitPost = async () => {
  const { valid } = await postForm.value.validate();
  if (valid) {
    posting.value = true;
    try {
      // 处理内容中的base64图片
      let processedContent = post.content;

      // 检查是否包含base64图片
      const base64ImageRegex = /<img[^>]+src="data:image\/[^;]+;base64,[^"]+"/g;
      if (base64ImageRegex.test(post.content)) {
        snackbar.text = "正在处理图片，请稍候...";
        snackbar.color = "info";
        snackbar.show = true;

        // 动态导入图片处理工具
        const { processImagesInContent } = await import("@/utils/imageUpload");
        processedContent = await processImagesInContent(post.content);
      }

      await http.post("/posts", {
        title: post.title,
        content: processedContent,
      });
      posting.value = false;
      snackbar.text = "帖子发布成功！";
      snackbar.color = "success";
      snackbar.show = true;
      // 清空表单
      post.title = "";
      post.content = "";
      // 跳转到社区或其他页面
      setTimeout(() => router.push("/community"), 1000);
    } catch (e) {
      posting.value = false;
      snackbar.text = e.message || "发布失败";
      snackbar.color = "error";
      snackbar.show = true;
    }
  }
};

// 图片上传事件处理
const handleImageAdded = async (file, insertImage) => {
  const formData = new FormData();
  formData.append("image", file);
  try {
    const res = await http.post("/upload", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
    const url = res.data.url || res.url;
    insertImage(url);
  } catch (e) {
    snackbar.text = "图片上传失败";
    snackbar.color = "error";
    snackbar.show = true;
  }
};
</script>

<style scoped>
.quill-editor-style {
  min-height: 200px;
}
</style>
