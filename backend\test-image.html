<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
            margin: 10px 0;
        }

        .error {
            color: red;
            font-weight: bold;
        }

        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <h1>图片显示测试</h1>

    <div class="test-section">
        <h2>测试1: 直接访问图片文件</h2>
        <p>尝试访问上传的图片文件：</p>
        <img src="/images/editor-image-1750169032889-0.jpg" alt="测试图片1"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
        <div class="error" style="display:none;">❌ 图片加载失败 - 文件不存在或路径错误</div>

        <img src="/images/editor-image-1750169032889-0.png" alt="测试图片2"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
        <div class="error" style="display:none;">❌ 图片加载失败 - 文件不存在或路径错误</div>

        <img src="/images/editor-image-1750169032889-0" alt="测试图片3"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
        <div class="error" style="display:none;">❌ 图片加载失败 - 文件不存在或路径错误</div>
    </div>

    <div class="test-section">
        <h2>测试2: 检查文件列表</h2>
        <p>当前 /images 目录下的文件：</p>
        <div id="file-list">加载中...</div>
    </div>

    <script>
        // 尝试获取文件列表（如果有API的话）
        fetch('/api/upload/list')
            .then(response => response.json())
            .then(data => {
                document.getElementById('file-list').innerHTML =
                    data.files ? data.files.join('<br>') : '无法获取文件列表';
            })
            .catch(error => {
                document.getElementById('file-list').innerHTML =
                    '无法获取文件列表 - 可能需要手动检查服务器文件';
            });
    </script>
</body>

</html>