<template>
  <v-container fluid class="pa-0 chat-wrapper">
    <!-- 主内容区 -->
    <v-row class="ma-6" no-gutters>
      <!-- 对话区 -->
      <v-col cols="12" md="9" class="pr-6 d-flex">
        <v-card class="chat-container flex-grow-1">
          <!-- 对话头部 -->
          <v-card-item class="chat-header">
            <div class="d-flex align-center">
              <span class="text-h6">与AI对话</span>
              <v-spacer />
              <v-btn
                color="primary"
                variant="tonal"
                @click="newChat"
                class="ml-4"
              >
                新增聊天
              </v-btn>
            </div>
          </v-card-item>

          <!-- 对话内容区 -->
          <v-card-text class="chat-body pa-6" ref="chatWindow">
            <!-- 欢迎消息 -->
            <v-alert
              v-if="!currentAiMbti && messages.length === 0"
              color="primary"
              variant="tonal"
              class="welcome-message"
            >
              <div class="text-center text-body-1">
                <strong>👋 欢迎来到MBTI AI对话!</strong><br />
                你可以直接告诉我想要和哪种MBTI类型的人对话<br />
                例如输入：<span class="text-primary">"我想和INFJ对话"</span> 或
                <span class="text-primary">"请让我和ENTJ聊聊"</span><br />
                所有16种MBTI类型都可以选择哦！
              </div>
            </v-alert>

            <!-- 聊天消息 -->
            <div
              v-for="(message, index) in messages"
              :key="index"
              class="message-row"
              :class="{ 'user-message': message.isUser }"
            >
              <div
                class="d-flex"
                :class="message.isUser ? 'justify-end' : 'justify-start'"
              >
                <div
                  class="d-flex"
                  :class="message.isUser ? 'flex-row-reverse' : 'flex-row'"
                >
                  <v-avatar
                    :color="
                      message.isUser
                        ? 'primary'
                        : getMbtiTypeColor(currentAiMbti).color
                    "
                    size="36"
                    class="ma-2"
                  >
                    <span class="text-white">{{
                      message.isUser ? "我" : currentAiMbti || "AI"
                    }}</span>
                  </v-avatar>
                  <v-card
                    :color="message.isUser ? 'primary' : undefined"
                    :class="message.isUser ? 'text-white' : 'bg-grey-lighten-4'"
                    class="ma-2 message-bubble"
                    variant="flat"
                    max-width="70%"
                  >
                    <v-card-text>
                      <!-- 加载动画 -->
                      <div v-if="message.loading" class="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <!-- 消息内容 -->
                      <div
                        v-else
                        class="markdown-body"
                        v-html="renderMessage(message.text)"
                      ></div>
                    </v-card-text>
                  </v-card>
                </div>
              </div>
            </div>
          </v-card-text>

          <!-- 输入区域 -->
          <v-card-actions class="chat-footer pa-4">
            <v-text-field
              v-model="inputMessage"
              placeholder="告诉我你想和哪种MBTI类型对话..."
              variant="outlined"
              density="comfortable"
              hide-details
              @keyup.enter="sendMessage"
              class="mr-4"
            ></v-text-field>
            <v-btn
              color="primary"
              :loading="aiDialog.loading"
              @click="sendMessage"
            >
              发送
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>

      <!-- 历史记录侧栏 -->
      <v-col cols="12" md="3" class="d-flex">
        <v-card class="chat-history flex-grow-1">
          <v-card-item>
            <v-card-title>聊天历史记录</v-card-title>
          </v-card-item>

          <v-card-text class="chat-history-list">
            <v-sheet
              v-if="chatHistory.length === 0"
              class="text-center pa-4 text-medium-emphasis"
            >
              暂无历史记录
            </v-sheet>

            <v-card
              v-for="(history, index) in chatHistory"
              :key="index"
              variant="tonal"
              class="mb-3"
              @click="selectChat(history)"
              style="cursor: pointer"
            >
              <v-card-text>
                <div class="text-primary font-weight-medium mb-1">
                  {{ history.title }}
                </div>
                <div class="text-medium-emphasis text-truncate">
                  {{ history.summary || history.lastMessage }}
                </div>
                <div class="text-caption text-grey">
                  {{ new Date(history.started_at).toLocaleString() }}
                </div>
              </v-card-text>
            </v-card>
          </v-card-text>

          <v-card-actions class="pa-4">
            <v-btn
              block
              color="error"
              variant="tonal"
              @click="clearHistory"
              :disabled="chatHistory.length === 0"
            >
              清空历史
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted } from "vue";
import { useAiDialogStore } from "@/stores/ai-dialog";
import { marked } from "marked";
import hljs from "highlight.js";
import "highlight.js/styles/github-dark.css";
import DOMPurify from "dompurify";
import http from "@/plugins/axios";

// 配置 marked
marked.setOptions({
  highlight: function (code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      return hljs.highlight(code, { language: lang }).value;
    }
    return code;
  },
  breaks: true,
  gfm: true,
});

// HTML 转义函数
function escape(html) {
  return html
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#39;");
}

// 状态变量
const currentAiMbti = computed(() => aiDialog.currentMbti);
const inputMessage = ref("");
const messages = computed(() => aiDialog.currentMessages);
const chatHistory = computed(() => aiDialog.sortedConversations);
const aiDialog = useAiDialogStore();

// 渲染消息内容
function renderMessage(text) {
  if (!text) return "";
  const html = marked(text);
  return DOMPurify.sanitize(html);
}

// 获取MBTI类型颜色
const getMbtiTypeColor = () => {
  return {
    color: "primary",
  };
};

// 封装滚动到底部方法，直接用 class 选择器
function scrollToBottom() {
  nextTick(() => {
    const el = document.querySelector(".chat-body");
    if (el) {
      el.scrollTop = el.scrollHeight;
    }
  });
}

// 发送消息
async function sendMessage() {
  const text = inputMessage.value.trim();
  if (!text) return;

  inputMessage.value = "";

  try {
    await aiDialog.sendMessage(text);
    scrollToBottom(); // 你发消息后滚动
  } catch (error) {
    console.error("AI对话出错：", error);
  }
}

// 监听消息数量变化，AI回复后滚动
watch(
  () => messages.value.length,
  () => {
    scrollToBottom();
  }
);

// 选择历史对话
async function selectChat(conversation) {
  try {
    await aiDialog.loadConversationMessages(conversation.id);
  } catch (error) {
    console.error("加载对话失败：", error);
  }
}

// 清空历史记录
async function clearHistory() {
  await aiDialog.clearHistory();
}

// 新增聊天
function newChat() {
  aiDialog.currentConversation = null;
  aiDialog.messages = [];
  aiDialog.currentMbti = null;
}

// 页面加载时执行
onMounted(async () => {
  await aiDialog.loadConversations();
});
</script>

<style scoped>
.chat-wrapper {
  min-height: calc(100vh - 90px);
  max-height: calc(100vh - 90px);
}

.chat-container,
.chat-history {
  min-height: calc(100vh - 112px); /* 减去导航栏(64px)和页面边距(48px)的高度 */
  max-height: calc(100vh - 112px);
  display: flex;
  flex-direction: column;
}

.chat-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.chat-body {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
.chat-body::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.chat-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.message-bubble {
  border-radius: 16px;
}

.chat-history-list {
  flex: 1;
  overflow-y: auto;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
.chat-history-list::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.welcome-message {
  line-height: 1.8;
}

/* 加载动画样式 */
.loading-dots {
  display: flex;
  gap: 8px;
  padding: 8px;
  justify-content: center;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #666;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Markdown 样式 */
.markdown-body {
  font-size: 14px;
  line-height: 1.6;
  color: #24292e;
}

.markdown-body pre {
  background-color: #1e1e1e;
  border-radius: 6px;
  padding: 16px;
  margin: 8px 0;
  overflow: auto;
}

.markdown-body code {
  font-family: "Fira Code", monospace;
  font-size: 13px;
  padding: 2px 4px;
  border-radius: 3px;
  background-color: rgba(27, 31, 35, 0.05);
}

.markdown-body pre code {
  background-color: transparent;
  padding: 0;
  font-size: 14px;
}

.markdown-body p {
  margin: 8px 0;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 24px;
  margin: 8px 0;
}

.markdown-body blockquote {
  padding: 0 8px;
  color: #666;
  border-left: 4px solid #ddd;
  margin: 8px 0;
}

.markdown-body table {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

.markdown-body th,
.markdown-body td {
  border: 1px solid #ddd;
  padding: 8px;
}

.markdown-body tr:nth-child(even) {
  background-color: #f8f8f8;
}

.markdown-body img {
  max-width: 100%;
  height: auto;
}

/* 高亮代码的行号样式 */
.hljs-ln {
  padding: 8px 0;
  border-spacing: 0;
}

.hljs-ln td {
  padding: 0;
  border: none;
}

.hljs-ln-numbers {
  user-select: none;
  text-align: center;
  color: #666;
  border-right: 1px solid #999;
  vertical-align: top;
  padding-right: 8px !important;
}

.hljs-ln-code {
  padding-left: 8px !important;
}
</style>
