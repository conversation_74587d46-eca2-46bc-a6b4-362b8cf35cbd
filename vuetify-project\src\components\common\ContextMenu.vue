<template>
  <v-menu location="bottom end">
    <template v-slot:activator="{ props }">
      <v-btn icon variant="text" v-bind="props" size="small" :color="color">
        <v-icon>mdi-dots-vertical</v-icon>
      </v-btn>
    </template>

    <v-list>
      <!-- 举报选项只对他人的内容显示 -->
      <template v-if="!isOwner">
        <v-list-item @click="$emit('report')">
          <template v-slot:prepend>
            <v-icon color="warning" class="mr-3"
              >mdi-alert-circle-outline</v-icon
            >
          </template>
          <v-list-item-title>举报</v-list-item-title>
        </v-list-item>
      </template>

      <!-- 删除选项仅对自己的内容显示 -->
      <template v-if="isOwner">
        <v-list-item @click="$emit('delete')" color="error">
          <template v-slot:prepend>
            <v-icon color="error">mdi-delete-outline</v-icon>
          </template>
          <v-list-item-title>删除</v-list-item-title>
        </v-list-item>
      </template>
    </v-list>
  </v-menu>
</template>

<script setup>
defineProps({
  // 是否是内容的所有者
  isOwner: {
    type: Boolean,
    default: false,
  },
  // 菜单按钮的颜色
  color: {
    type: String,
    default: "grey",
  },
});

// 定义事件
defineEmits(["report", "delete"]);
</script>

<style scoped>
.v-list-item {
  min-height: 35px;
}

/* 减少图标和文字之间的间距 */
.v-list-item :deep(.v-list-item__prepend) {
  margin-inline-end: 8px !important;
}

.v-list-item :deep(.v-list-item__prepend .v-icon) {
  margin-inline-end: 0 !important;
}
</style>