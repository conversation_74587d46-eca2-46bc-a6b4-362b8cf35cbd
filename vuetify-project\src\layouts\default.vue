<template>
  <div>
    <v-app-bar app elevation="0" color="white" height="58">
      <v-container class="d-flex align-center px-6" fluid>
        <!-- Logo -->
        <router-link to="/" class="text-decoration-none">
          <span class="text-h6 font-weight-medium text-primary">MBTI-LINK</span>
        </router-link>

        <!-- 导航链接 -->
        <v-spacer></v-spacer>
        <div class="d-flex align-center">
          <v-btn
            v-for="item in navItems"
            :key="item.key"
            :to="item.link"
            variant="text"
            :class="{ 'active-link': activeLink === item.key }"
            @click="setActiveLink(item.key)"
            class="text-body-1 font-weight-regular mx-2"
            style="text-transform: none"
          >
            {{ item.name }}
          </v-btn>
        </div>

        <!-- 用户搜索框 -->
        <v-text-field
          v-model="searchInput"
          label="搜索用户"
          prepend-inner-icon="mdi-magnify"
          clearable
          solo-inverted
          dense
          variant="outlined"
          density="compact"
          class="mt-5"
          :loading="searchLoading"
          @keyup.enter="onSearchInput(searchInput)"
          @update:modelValue="onSearchInput"
        />

        <!-- 用户操作区 -->
        <v-spacer></v-spacer>
        <div class="d-flex align-center">
          <template v-if="!userStore.isLoggedIn">
            <v-btn
              variant="text"
              to="/login-register?tab=login"
              class="text-body-1 font-weight-regular"
              style="text-transform: none"
            >
              登录
            </v-btn>
            <v-btn
              color="primary"
              to="/login-register?tab=register"
              class="text-body-1 font-weight-regular ml-2"
              style="text-transform: none"
            >
              注册
            </v-btn>
          </template>

          <template v-else>
            <!-- 通知菜单 -->
            <NotificationMenu class="mr-2" />

            <v-menu location="bottom end">
              <template v-slot:activator="{ props }">
                <v-btn variant="text" v-bind="props" class="text-body-1">
                  {{ userStore.user?.name }}
                  <v-icon end>mdi-chevron-down</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item @click="goToUserProfile">
                  <v-list-item-title>个人中心</v-list-item-title>
                </v-list-item>
                <v-list-item @click="logout">
                  <v-list-item-title>退出登录</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </template>
        </div>
      </v-container>
    </v-app-bar>
    <v-main>
      <router-view />
    </v-main>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import NotificationMenu from "@/components/NotificationMenu.vue";
import http from "@/plugins/axios";

const router = useRouter();
const userStore = useUserStore();
const activeLink = ref("home");

const navItems = [
  { name: "首页", link: "/", key: "home" },
  { name: "社区", link: "/community", key: "community" },
  { name: "与MBTI模拟对话", link: "/chat", key: "chat" },
];

const setActiveLink = (key) => {
  activeLink.value = key;
};

const goToUserProfile = () => {
  if (userStore.userInfo && userStore.userInfo.uid) {
    router.push(`/profile/${userStore.userInfo.uid}`);
  } else {
    router.push("/login-register?tab=login");
  }
};

const logout = () => {
  userStore.logout();
  router.push("/");
};

// ===== 用户搜索相关 =====
const searchInput = ref("");
const searchResults = ref([]);
const searchLoading = ref(false);
let searchTimer = null;

const onSearchInput = (val) => {
  if (searchTimer) clearTimeout(searchTimer);
  if (!val) {
    searchResults.value = [];
    return;
  }
  searchLoading.value = true;
  searchTimer = setTimeout(async () => {
    try {
      const res = await http.get("/user/search", {
        keyword: val,
        page: 1,
        pageSize: 1,
      });
      const users = res.data || [];
      searchResults.value = users;
      if (users.length > 0) {
        router.push(`/profile/${users[0].uid}`);
        searchInput.value = "";
      }
    } catch (e) {
      searchResults.value = [];
    } finally {
      searchLoading.value = false;
    }
  }, 400);
};
</script>

<style scoped>
.active-link {
  color: rgb(var(--v-theme-primary)) !important;
}

.v-btn {
  letter-spacing: 0;
}
</style>
