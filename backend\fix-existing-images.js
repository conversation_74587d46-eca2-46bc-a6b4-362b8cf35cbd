const fs = require('fs');
const path = require('path');

// 修复已存在的图片文件，添加正确的扩展名
const imagesDir = path.join(__dirname, 'public/images');

console.log('开始修复现有图片文件...');

try {
    const files = fs.readdirSync(imagesDir);
    
    files.forEach(filename => {
        const filePath = path.join(imagesDir, filename);
        const stats = fs.statSync(filePath);
        
        // 跳过目录和已有扩展名的文件
        if (stats.isDirectory() || filename.includes('.')) {
            return;
        }
        
        console.log(`处理文件: ${filename}`);
        
        // 读取文件的前几个字节来判断图片类型
        const buffer = fs.readFileSync(filePath);
        let extension = '';
        
        // 检查文件头来确定图片类型
        if (buffer[0] === 0xFF && buffer[1] === 0xD8 && buffer[2] === 0xFF) {
            extension = '.jpg';
        } else if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) {
            extension = '.png';
        } else if (buffer[0] === 0x47 && buffer[1] === 0x49 && buffer[2] === 0x46) {
            extension = '.gif';
        } else if (buffer[0] === 0x52 && buffer[1] === 0x49 && buffer[2] === 0x46 && buffer[3] === 0x46) {
            extension = '.webp';
        } else {
            // 默认为 jpg
            extension = '.jpg';
        }
        
        const newFilename = filename + extension;
        const newFilePath = path.join(imagesDir, newFilename);
        
        // 重命名文件
        fs.renameSync(filePath, newFilePath);
        console.log(`✅ 重命名: ${filename} -> ${newFilename}`);
    });
    
    console.log('🎉 图片文件修复完成！');
    
} catch (error) {
    console.error('❌ 修复失败:', error.message);
}
